"""
MCP服务配置页面
"""
import json
import os
import streamlit as st
from functions.mcp_client import mcp_manager
from functions.mcp_server import mcp_server_manager


def sync_managers():
    """同步两个管理器的配置"""
    mcp_server_manager.config = mcp_manager.config


def show_service_logs():
    """显示服务日志"""
    # 获取所有服务日志
    all_logs = mcp_server_manager.get_all_server_logs()

    if not all_logs:
        st.info("📝 暂无服务日志")
        return

    # 为每个服务显示日志
    for server_name, logs in all_logs.items():
        status_icon = {
            "运行中": "🟢",
            "已结束": "🔴",
            "未运行": "⚪"
        }.get(logs.get('status', '未知状态').split(' ')[0], "❓")

        with st.expander(f"{status_icon} **{server_name}** - {logs.get('status', '未知状态')}"):
            # 显示基本信息
            col1, col2 = st.columns(2)
            with col1:
                st.markdown(f"**🔧 命令:** `{logs.get('command', 'N/A')}`")
            with col2:
                start_time = logs.get('start_time', 0)
                if start_time > 0:
                    import datetime
                    start_str = datetime.datetime.fromtimestamp(start_time).strftime("%Y-%m-%d %H:%M:%S")
                    st.markdown(f"**⏰ 启动时间:** {start_str}")

            # 显示标准输出
            stdout_logs = logs.get('stdout', [])
            if stdout_logs:
                st.markdown("**📤 标准输出:**")
                with st.container():
                    for log_line in stdout_logs[-20:]:  # 只显示最近20条
                        st.code(log_line, language="text")

            # 显示错误输出
            stderr_logs = logs.get('stderr', [])
            if stderr_logs:
                st.markdown("**❌ 错误输出:**")
                with st.container():
                    for log_line in stderr_logs[-20:]:  # 只显示最近20条
                        st.error(log_line)

            # 清除日志按钮
            col1, col2, col3 = st.columns([1, 1, 2])
            with col1:
                if st.button(f"🗑️ 清除日志", key=f"clear_logs_{server_name}"):
                    mcp_server_manager.clear_server_logs(server_name)
                    st.success(f"已清除 {server_name} 的日志")
                    st.rerun()


def render_mcp_config_page():
    """渲染MCP配置页面"""
    # MCP功能现在总是可用

    # 获取所有服务器配置
    try:
        servers = mcp_manager.get_all_servers()
        # 确保两个管理器配置同步
        sync_managers()
        server_status = mcp_server_manager.get_all_server_status()
    except Exception as e:
        st.error(f"获取MCP服务配置失败: {e}")
        return



    # === MCP服务日志 ===
    if servers:
        st.header("MCP服务日志")
        show_service_logs()

    # === MCP导入/导出配置 ===
    st.header("MCP导入/导出配置")

    col1, col2 = st.columns(2)

    with col1:
        # 导出配置
        if st.button("导出配置"):
            try:
                config_json = json.dumps(mcp_manager.config, indent=2, ensure_ascii=False)
                st.download_button(
                    label="下载配置文件",
                    data=config_json,
                    file_name="mcp_config.json",
                    mime="application/json"
                )
            except Exception as e:
                st.error(f"导出配置失败: {e}")

    with col2:
        # 导入配置
        uploaded_file = st.file_uploader("导入配置文件", type=["json"])
        if uploaded_file is not None:
            try:
                config = json.load(uploaded_file)
                if "mcpServers" in config:
                    # 先停止所有服务
                    mcp_server_manager.stop_all_servers()
                    # 更新配置
                    mcp_manager.config = config
                    mcp_manager._save_config(config)
                    sync_managers()
                    # 启动所有启用的服务
                    mcp_server_manager.start_all_enabled_servers()
                    st.success("配置导入成功")
                    st.rerun()
                else:
                    st.error("配置文件格式错误，缺少 mcpServers 字段")
            except json.JSONDecodeError:
                st.error("配置文件格式错误，请上传有效的JSON文件")
            except Exception as e:
                st.error(f"导入配置失败: {e}")




# 如果直接运行此文件，则显示配置页面
if __name__ == "__main__":
    render_mcp_config_page()

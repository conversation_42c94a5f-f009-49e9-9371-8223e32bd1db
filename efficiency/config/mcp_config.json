{"mcpServers": {"@smithery-ai-server-sequential-thinking": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@smithery-ai/server-sequential-thinking", "--config", "{}"], "enabled": false}, "@smithery-ai-fetch": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@smithery-ai/fetch", "--config", "{}"], "enabled": false}, "files": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/app/data"], "enabled": true}, "@wopal-mcp-server-hotnews": {"command": "npx", "args": ["@wopal/mcp-server-hotnews"], "enabled": false}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "enabled": false}, "hn-server": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@pskill9/hn-server"], "enabled": false}, "duckduckgo-mcp-server": {"connection_type": "http", "server_name": "@nickclyde/duckduckgo-mcp-server", "api_key": "", "profile_id": "", "config": {}, "enabled": false}, "browserbase": {"connection_type": "http", "server_name": "@browserbasehq/mcp-browserbase", "api_key": "", "profile_id": "", "config": {"browserbaseApiKey": "", "browserbaseProjectId": ""}, "enabled": false}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"], "enabled": false}, "time-server": {"connection_type": "stdio", "command": "python3", "args": ["/app/mcp_servers/time_server.py"], "enabled": true}, "demo-server": {"connection_type": "http", "server_name": "@demo/simple-server", "api_key": "demo-key", "profile_id": "", "config": {}, "enabled": false, "description": "演示用的远程MCP服务（需要真实的Smithery API密钥）"}}}
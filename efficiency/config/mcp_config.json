{"mcpServers": {"@smithery-ai-server-sequential-thinking": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@smithery-ai/server-sequential-thinking", "--config", "{}"], "enabled": false}, "@smithery-ai-fetch": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@smithery-ai/fetch", "--config", "{}"], "enabled": false}, "files": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/app/data"], "enabled": true}, "@wopal-mcp-server-hotnews": {"command": "npx", "args": ["@wopal/mcp-server-hotnews"], "enabled": false}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "enabled": false}, "hn-server": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@pskill9/hn-server"], "enabled": false}, "duckduckgo-mcp-server": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@nickclyde/duckduckgo-mcp-server"], "enabled": false}}}
{"mcpServers": {"files": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/app/data"], "enabled": true}, "@wopal-mcp-server-hotnews": {"command": "npx", "args": ["@wopal/mcp-server-hotnews"], "enabled": false}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "enabled": false}, "duckduckgo-mcp-server": {"command": "npx", "args": ["-y", "@nickclyde/duckduckgo-mcp-server"], "connection_type": "stdio", "enabled": true}, "browserbase": {"command": "npx", "args": ["-y", "@browserbasehq/mcp-browserbase"], "connection_type": "stdio", "env": {"BROWSERBASE_API_KEY": "", "BROWSERBASE_PROJECT_ID": ""}, "enabled": false}}}
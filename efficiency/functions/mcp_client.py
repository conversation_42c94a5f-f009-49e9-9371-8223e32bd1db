"""
MCP客户端实现
"""
import json
import os
import logging
from typing import Dict, Any, List, Optional, Tuple, Callable
import asyncio
from functools import partial

# 导入MCP相关模块
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

MCP_AVAILABLE = True


class MCPManager:
    """MCP管理器，负责管理MCP服务"""

    def __init__(self, config_path: str = "config/mcp_config.json"):
        """初始化MCP管理器

        Args:
            config_path: MCP配置文件路径
        """
        self.config_path = config_path
        self.clients = {}  # 存储已连接的MCP客户端
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """加载MCP配置

        Returns:
            MCP配置
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            else:
                # 创建默认配置
                default_config = {"mcpServers": {}}
                self._save_config(default_config)
                return default_config
        except Exception as e:
            logging.error(f"加载MCP配置失败: {e}")
            return {"mcpServers": {}}

    def _save_config(self, config: Dict[str, Any]) -> None:
        """保存MCP配置

        Args:
            config: MCP配置
        """
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"保存MCP配置失败: {e}")

    def get_server_config(self, server_name: str) -> Optional[Dict[str, Any]]:
        """获取服务器配置

        Args:
            server_name: 服务器名称

        Returns:
            服务器配置
        """
        return self.config.get("mcpServers", {}).get(server_name)

    def get_all_servers(self) -> Dict[str, Dict[str, Any]]:
        """获取所有服务器配置

        Returns:
            所有服务器配置
        """
        return self.config.get("mcpServers", {})

    def update_server_config(self, server_name: str, config: Dict[str, Any]) -> None:
        """更新服务器配置

        Args:
            server_name: 服务器名称
            config: 服务器配置
        """
        if "mcpServers" not in self.config:
            self.config["mcpServers"] = {}
        self.config["mcpServers"][server_name] = config
        self._save_config(self.config)

    def remove_server_config(self, server_name: str) -> None:
        """删除服务器配置

        Args:
            server_name: 服务器名称
        """
        if "mcpServers" in self.config and server_name in self.config["mcpServers"]:
            del self.config["mcpServers"][server_name]
            self._save_config(self.config)

    def toggle_server_status(self, server_name: str, enabled: bool) -> None:
        """切换服务器状态

        Args:
            server_name: 服务器名称
            enabled: 是否启用
        """
        if "mcpServers" in self.config and server_name in self.config["mcpServers"]:
            self.config["mcpServers"][server_name]["enabled"] = enabled
            self._save_config(self.config)

    async def connect_to_server(self, server_name: str) -> bool:
        """连接到MCP服务器

        Args:
            server_name: 服务器名称

        Returns:
            是否连接成功
        """
        server_config = self.get_server_config(server_name)
        if not server_config:
            logging.error(f"服务器配置不存在: {server_name}")
            return False

        if not server_config.get("enabled", False):
            logging.error(f"服务器未启用: {server_name}")
            return False

        try:
            # 判断连接类型
            connection_type = server_config.get("connection_type", "stdio")

            if connection_type == "http":
                # HTTP连接（用于Smithery远程服务）- 使用Python MCP SDK
                from .http_mcp_client import HTTPMCPClient as NewHTTPMCPClient
                client = NewHTTPMCPClient(server_config)
                logging.info(f"创建HTTP MCP客户端: {server_name}")
            else:
                # Stdio连接（用于本地服务）
                if "command" not in server_config:
                    logging.error(f"Stdio连接需要command配置: {server_name}")
                    return False
                server_params = StdioServerParameters(
                    command=server_config["command"],
                    args=server_config["args"],
                    env=server_config.get("env")
                )
                client = MCPClient(server_params)
                logging.info(f"创建Stdio MCP客户端: {server_name}")

            await client.connect()
            self.clients[server_name] = client
            return True
        except Exception as e:
            logging.error(f"连接到MCP服务器失败: {server_name}, 错误: {e}")
            return False

    async def disconnect_from_server(self, server_name: str) -> bool:
        """断开与MCP服务器的连接

        Args:
            server_name: 服务器名称

        Returns:
            是否断开连接成功
        """
        if server_name in self.clients:
            try:
                await self.clients[server_name].disconnect()
                del self.clients[server_name]
                return True
            except Exception as e:
                logging.error(f"断开与MCP服务器的连接失败: {server_name}, 错误: {e}")
                return False
        return True

    async def get_available_tools(self, server_name: str) -> List[Any]:
        """获取可用工具

        Args:
            server_name: 服务器名称

        Returns:
            可用工具列表
        """
        if server_name not in self.clients:
            if not await self.connect_to_server(server_name):
                return []

        try:
            return await self.clients[server_name].get_available_tools()
        except Exception as e:
            logging.error(f"获取可用工具失败: {server_name}, 错误: {e}")
            return []

    async def call_tool(self, server_name: str, tool_name: str, **kwargs) -> Any:
        """调用工具

        Args:
            server_name: 服务器名称
            tool_name: 工具名称
            **kwargs: 工具参数

        Returns:
            工具调用结果
        """
        if server_name not in self.clients:
            if not await self.connect_to_server(server_name):
                return None

        try:
            tool_callable = self.clients[server_name].call_tool(tool_name)
            return await tool_callable(**kwargs)
        except Exception as e:
            logging.error(f"调用工具失败: {server_name}.{tool_name}, 错误: {e}")
            return None


class HTTPMCPClient:
    """HTTP MCP客户端（用于Smithery远程服务）"""

    def __init__(self, server_config: Dict[str, Any]):
        """初始化HTTP MCP客户端

        Args:
            server_config: 服务器配置
        """
        self.server_config = server_config
        self.session = None
        self._client = None
        self.initialized = False

    async def _parse_response(self, response):
        """解析HTTP响应，支持JSON和SSE格式"""
        content_type = response.headers.get('content-type', '')
        result = None

        # 总是先读取文本，避免aiohttp自动JSON解析
        text = await response.text()

        if 'text/event-stream' in content_type:
            # 解析SSE格式的JSON响应
            lines = text.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('data: '):
                    json_data = line[6:]  # 移除 'data: ' 前缀
                    if json_data.strip():
                        import json
                        try:
                            result = json.loads(json_data)
                            break
                        except json.JSONDecodeError as e:
                            logging.warning(f"JSON解析失败: {e}, 数据: {json_data[:100]}")
                            continue
        else:
            # 手动解析JSON
            import json
            try:
                result = json.loads(text)
            except json.JSONDecodeError as e:
                logging.error(f"JSON解析失败: {e}, 响应: {text[:200]}")
                result = None

        return result

    async def get_server_info(self, api_key: str, server_name: str) -> dict:
        """从Registry API获取服务器信息"""
        import aiohttp

        registry_url = f"https://registry.smithery.ai/servers/{server_name}"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Accept": "application/json"
        }

        async with aiohttp.ClientSession() as session:
            async with session.get(registry_url, headers=headers) as response:
                if response.status == 200:
                    text = await response.text()
                    import json
                    return json.loads(text)
                else:
                    raise Exception(f"获取服务器信息失败，状态码: {response.status}, 响应: {await response.text()}")

    async def connect(self) -> None:
        """连接到HTTP MCP服务器"""
        import aiohttp

        # 构建Smithery URL和认证
        server_name = self.server_config.get("server_name", "")
        api_key = self.server_config.get("api_key", "")
        profile_id = self.server_config.get("profile_id", "")

        # 先从Registry API获取服务器信息
        try:
            server_info = await self.get_server_info(api_key, server_name)
            logging.info(f"获取到服务器信息: {server_info.get('displayName', server_name)}")
        except Exception as e:
            logging.warning(f"获取服务器信息失败: {e}，继续尝试连接")

        # 构建正确的Smithery URL（使用api_key参数）
        if profile_id:
            self.base_url = f"https://server.smithery.ai/{server_name}/mcp?profile={profile_id}&api_key={api_key}"
        else:
            # 手动配置方式
            config = self.server_config.get("config", {})
            import urllib.parse
            config_str = urllib.parse.urlencode(config)
            if config_str:
                self.base_url = f"https://server.smithery.ai/{server_name}/mcp?{config_str}&api_key={api_key}"
            else:
                self.base_url = f"https://server.smithery.ai/{server_name}/mcp?api_key={api_key}"

        # 设置请求头（不需要Authorization，使用URL中的api_key）
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/event-stream"
        }

        # 创建HTTP会话，禁用自动JSON解析
        connector = aiohttp.TCPConnector(limit=10)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=aiohttp.ClientTimeout(total=30)
        )

        # 测试连接
        try:
            async with self.session.post(
                self.base_url,
                json={
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "initialize",
                    "params": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {},
                        "clientInfo": {
                            "name": "efficiency-mcp-client",
                            "version": "1.0.0"
                        }
                    }
                },
                headers=self.headers
            ) as response:
                if response.status == 200:
                    result = await self._parse_response(response)

                    if result is None:
                        raise Exception("无法解析服务器响应")

                    logging.info(f"HTTP MCP连接成功: {server_name}")
                    self.initialized = True
                else:
                    raise Exception(f"HTTP连接失败，状态码: {response.status}")
        except Exception as e:
            if self.session:
                await self.session.close()
            raise e

    async def disconnect(self) -> None:
        """断开HTTP连接"""
        if self.session:
            await self.session.close()
            self.session = None

    async def get_available_tools(self) -> List[Any]:
        """获取可用工具"""
        if not self.session or not hasattr(self, 'initialized'):
            raise RuntimeError("未连接到HTTP MCP服务器")

        try:
            async with self.session.post(
                self.base_url,
                json={
                    "jsonrpc": "2.0",
                    "id": 2,
                    "method": "tools/list",
                    "params": {}
                },
                headers=self.headers
            ) as response:
                if response.status == 200:
                    result = await self._parse_response(response)

                    if result and "result" in result and "tools" in result["result"]:
                        # 创建简单的工具对象
                        tools = []
                        for tool_data in result["result"]["tools"]:
                            tool = type('Tool', (), {
                                'name': tool_data.get('name', ''),
                                'description': tool_data.get('description', ''),
                                'inputSchema': tool_data.get('inputSchema', {})
                            })()
                            tools.append(tool)
                        return tools
                    return []
                else:
                    raise Exception(f"获取工具列表失败，状态码: {response.status}")
        except Exception as e:
            logging.error(f"获取HTTP MCP工具列表失败: {e}")
            return []

    def call_tool(self, tool_name: str) -> Callable:
        """调用工具"""
        if not self.session or not hasattr(self, 'initialized'):
            raise RuntimeError("未连接到HTTP MCP服务器")

        async def callable(*args, **kwargs):
            try:
                async with self.session.post(
                    self.base_url,
                    json={
                        "jsonrpc": "2.0",
                        "id": 3,
                        "method": "tools/call",
                        "params": {
                            "name": tool_name,
                            "arguments": kwargs
                        }
                    },
                    headers=self.headers
                ) as response:
                    if response.status == 200:
                        result = await self._parse_response(response)

                        if result and "result" in result and "content" in result["result"]:
                            # 提取文本内容
                            content = result["result"]["content"]
                            if isinstance(content, list) and len(content) > 0:
                                return content[0].get("text", str(content))
                            return str(content)
                        return str(result.get("result", "") if result else "")
                    else:
                        raise Exception(f"工具调用失败，状态码: {response.status}")
            except Exception as e:
                logging.error(f"HTTP MCP工具调用失败: {e}")
                raise e

        return callable


class MCPClient:
    """MCP客户端"""

    def __init__(self, server_params: Any):
        """初始化MCP客户端

        Args:
            server_params: 服务器参数
        """
        self.server_params = server_params
        self.session = None
        self._client = None

    async def connect(self) -> None:
        """连接到MCP服务器"""
        self._client = stdio_client(self.server_params)
        self.read, self.write = await self._client.__aenter__()
        session = ClientSession(self.read, self.write)
        self.session = await session.__aenter__()
        await self.session.initialize()

    async def disconnect(self) -> None:
        """断开与MCP服务器的连接"""
        if self.session:
            await self.session.__aexit__(None, None, None)
        if self._client:
            await self._client.__aexit__(None, None, None)

    async def get_available_tools(self) -> List[Any]:
        """获取可用工具

        Returns:
            可用工具列表
        """
        if not self.session:
            raise RuntimeError("未连接到MCP服务器")

        tools = await self.session.list_tools()
        _, tools_list = tools
        _, tools_list = tools_list
        return tools_list

    def call_tool(self, tool_name: str) -> Callable:
        """调用工具

        Args:
            tool_name: 工具名称

        Returns:
            工具调用函数
        """
        if not self.session:
            raise RuntimeError("未连接到MCP服务器")

        async def callable(*args, **kwargs):
            response = await self.session.call_tool(tool_name, arguments=kwargs)
            return response.content[0].text

        return callable


# 创建MCP管理器实例
mcp_manager = MCPManager()


def use_mcp_tool(server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, str]:
    """使用MCP工具

    Args:
        server_name: 服务器名称
        tool_name: 工具名称
        arguments: 工具参数

    Returns:
        (成功标志, 结果)
    """
    async def _async_use_mcp_tool():
        try:
            # 确保arguments是字典类型
            current_arguments = arguments
            if isinstance(current_arguments, str):
                import json
                current_arguments = json.loads(current_arguments)
            elif not isinstance(current_arguments, dict):
                current_arguments = {}

            # 获取所有启用的服务器
            enabled_servers = {}
            for name, config in mcp_manager.get_all_servers().items():
                if config.get("enabled", False):
                    enabled_servers[name] = config

            current_server_name = server_name

            # 如果指定的服务器不存在或未启用，尝试根据工具名称自动选择服务器
            if current_server_name not in enabled_servers:
                logging.warning(f"指定的服务器不存在或未启用: {current_server_name}，尝试自动选择服务器")

                # 获取所有服务器的工具列表
                server_tools = {}
                for name in enabled_servers:
                    try:
                        tools = await mcp_manager.get_available_tools(name)
                        server_tools[name] = [tool.name for tool in tools]
                    except Exception as e:
                        logging.warning(f"获取服务器 {name} 的工具列表失败: {e}")
                        continue

                # 查找包含指定工具的服务器
                matching_servers = []
                for name, tools in server_tools.items():
                    if tool_name in tools:
                        matching_servers.append(name)

                if matching_servers:
                    # 使用第一个匹配的服务器
                    current_server_name = matching_servers[0]
                    logging.info(f"自动选择服务器: {current_server_name}")
                else:
                    return False, f"找不到提供工具 {tool_name} 的服务器。启用的服务器: {list(enabled_servers.keys())}"

            # 清理可能存在的旧连接
            if current_server_name in mcp_manager.clients:
                try:
                    await mcp_manager.disconnect_from_server(current_server_name)
                except Exception as e:
                    logging.warning(f"清理旧连接失败: {e}")

            # 重新连接到服务器
            success = await mcp_manager.connect_to_server(current_server_name)
            if not success:
                return False, f"连接到MCP服务器失败: {current_server_name}"

            # 调用工具
            result = await mcp_manager.call_tool(current_server_name, tool_name, **current_arguments)

            if result is None:
                return False, f"调用工具失败: {current_server_name}.{tool_name}"

            return True, str(result)
        except Exception as e:
            logging.error(f"使用MCP工具失败: {server_name}.{tool_name}, 错误: {e}")
            return False, f"使用MCP工具失败: {e}"

    try:
        # 尝试获取当前事件循环
        try:
            loop = asyncio.get_running_loop()
            # 如果已有事件循环在运行，使用 asyncio.create_task
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, _async_use_mcp_tool())
                return future.result(timeout=30)
        except RuntimeError:
            # 没有运行中的事件循环，直接运行
            return asyncio.run(_async_use_mcp_tool())
    except Exception as e:
        logging.error(f"事件循环处理失败: {e}")
        return False, f"事件循环处理失败: {e}"


def access_mcp_resource(server_name: str, uri: str) -> Tuple[bool, str]:
    """访问MCP资源

    Args:
        server_name: 服务器名称
        uri: 资源URI

    Returns:
        (成功标志, 结果)
    """
    try:
        # 创建事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # 获取所有启用的服务器
        enabled_servers = {}
        for name, config in mcp_manager.get_all_servers().items():
            if config.get("enabled", False):
                enabled_servers[name] = config

        # 如果指定的服务器不存在或未启用，尝试根据URI自动选择服务器
        if server_name not in enabled_servers:
            logging.warning(f"指定的服务器不存在或未启用: {server_name}，尝试自动选择服务器")

            # 尝试根据URI前缀匹配服务器
            uri_prefix = uri.split("://")[0] if "://" in uri else ""
            matching_servers = []

            for name in enabled_servers:
                # 简单匹配：如果URI前缀包含在服务器名称中，或者服务器名称包含在URI前缀中
                if (uri_prefix and (uri_prefix.lower() in name.lower() or name.lower() in uri_prefix.lower())):
                    matching_servers.append(name)

            if matching_servers:
                # 使用第一个匹配的服务器
                server_name = matching_servers[0]
                logging.info(f"自动选择服务器: {server_name}")
            else:
                # 如果没有匹配，使用第一个启用的服务器
                if enabled_servers:
                    server_name = list(enabled_servers.keys())[0]
                    logging.info(f"未找到匹配的服务器，使用第一个启用的服务器: {server_name}")
                else:
                    loop.close()
                    return False, "没有启用的MCP服务器"

        # 连接到服务器
        if server_name not in mcp_manager.clients:
            success = loop.run_until_complete(mcp_manager.connect_to_server(server_name))
            if not success:
                loop.close()
                return False, f"连接到MCP服务器失败: {server_name}"

        # 访问资源
        if mcp_manager.clients[server_name].session:
            response = loop.run_until_complete(mcp_manager.clients[server_name].session.access_resource(uri))
            loop.close()
            return True, response.content[0].text
        else:
            loop.close()
            return False, f"未连接到MCP服务器: {server_name}"
    except Exception as e:
        logging.error(f"访问MCP资源失败: {server_name}.{uri}, 错误: {e}")
        return False, f"访问MCP资源失败: {e}"

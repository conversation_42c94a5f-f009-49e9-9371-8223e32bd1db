services:
  app:
    container_name: llm_assitant
    build: .
    ports:
      - "8501:8501"
    volumes:
      - ./data:/app/data
      - ./config:/app/config
      - npm_cache:/home/<USER>/.npm
    environment:
      - PYTHONUNBUFFERED=1
      - NODE_ENV=production
    restart: unless-stopped
    networks:
      - efficiency_network
    dns:
      - *******
      - *******
    extra_hosts:
      - "server.smithery.ai:*************"
      - "registry.smithery.ai:*************"

volumes:
  npm_cache:

networks:
  efficiency_network:
    driver: bridge
    driver_opts:
      com.docker.network.enable_ipv6: "false"
